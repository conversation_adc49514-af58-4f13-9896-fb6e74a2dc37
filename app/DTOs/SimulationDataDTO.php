<?php

namespace App\DTOs;

class SimulationDataDTO
{
    public function __construct(
        public array $fixtures,
        public array $standings,
        public array $predictions,
        public int $currentWeek,
        public bool $seasonComplete,
        public string $seasonId
    ) {}

    public function toArray(): array
    {
        return [
            'fixtures' => $this->fixtures,
            'standings' => $this->standings,
            'predictions' => $this->predictions,
            'current_week' => $this->currentWeek,
            'season_complete' => $this->seasonComplete,
            'seasonId' => $this->seasonId
        ];
    }

    public function toApiResponse(): array
    {
        return [
            'fixtures' => $this->fixtures,
            'standings' => $this->standings,
            'predictions' => $this->predictions,
            'current_week' => $this->currentWeek,
            'season_complete' => $this->seasonComplete,
        ];
    }
}
