<?php

namespace App\Http\Controllers;

use App\Http\Resources\LeagueMatchResource;
use App\Models\LeagueMatches;
use App\Models\Team;
use App\Services\FixtureGenerator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class LeagueController extends Controller
{
    public function getTeams()
    {
        $teams = Team::all();

        return Inertia::render('Teams', [
            'teams' => $teams
        ]);
    }


    public function startSimulation(FixtureGenerator $generator): RedirectResponse
    {
        $teamIds = Team::pluck('id')->toArray();

        $seasonId = Str::random(8);
        $generator->generate($teamIds, $seasonId);

        // Set cookie and redirect to simulation page
        return redirect()->route('simulation')
            ->cookie('simulation_season_id', $seasonId, 60 * 24 * 7); // 7 days
    }


    public function simulationIndex(Request $request): Response|RedirectResponse
    {
        $cookieSeasonId = $request->cookie('simulation_season_id');

        if ($cookieSeasonId && LeagueMatches::where('season_id', $cookieSeasonId)->exists()) {
            $seasonId = $cookieSeasonId;
        } else {
            return redirect()->route('/');
        }

        return Inertia::render('Simulation', [
            'fixtures' => $this->getUpdatedFixtures($seasonId),
            'seasonId' => $seasonId
        ]);
    }

    public function playAllWeeks(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'season_id' => ['required', 'string', 'size:8', 'alpha_num'],
        ]);

        $seasonId = $validated['season_id'];

        $matches = LeagueMatches::where('is_played', false)
            ->where('season_id', $seasonId)
            ->get();

        $updateCount = 0;
        foreach ($matches as $match) {
            $updateCount += $this->simulateMatchAsPlayed($match);
        }

        return response()->json([
            'played_count' => $updateCount,
            'fixtures' => $this->getUpdatedFixtures($seasonId),
        ]);
    }

    public function playNextWeek(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'season_id' => ['required', 'string', 'size:8', 'alpha_num'],
        ]);

        $seasonId = $validated['season_id'];

        $nextWeek = LeagueMatches::where('season_id', $seasonId)
            ->where('is_played', false)
            ->min('week');

        if (is_null($nextWeek)) {
            return response()->json([
                'message' => 'All matches have already been played for this season.'
            ]);
        }

        $unplayedMatches = LeagueMatches::where('season_id', $seasonId)
            ->where('week', $nextWeek)
            ->where('is_played', false)
            ->get();

        $updateCount = 0;
        foreach ($unplayedMatches as $match) {
            $updateCount += $this->simulateMatchAsPlayed($match);
        }

        return response()->json([
            'message' => "Week {$nextWeek} matches played.",
            'played_count' => $updateCount,
            'week' => $nextWeek,
            'fixtures' => $this->getUpdatedFixtures($seasonId)
        ]);
    }


    /**
     * @param mixed $match
     * @return bool
     */
    public function simulateMatchAsPlayed(mixed $match): bool
    {
        return $match->update([
            'home_score' => rand(0, 5),
            'away_score' => rand(0, 5),
            'is_played' => true,
            'played_at' => now(),
        ]);
    }


    public function getUpdatedFixtures(string $seasonId): array
    {
        $updatedFixtures = LeagueMatches::with(['homeTeam', 'awayTeam'])
            ->where('season_id', $seasonId)
            ->orderBy('week')
            ->orderBy('id')
            ->get();

        return LeagueMatchResource::collection($updatedFixtures)->resolve();
    }
}
