<?php

namespace App\Services;

use App\Models\LeagueMatches;

class SimulationService
{
    public function calculateStandings(string $seasonId): array
    {
        $matches = LeagueMatches::with(['homeTeam', 'awayTeam'])
            ->where('season_id', $seasonId)
            ->where('is_played', true)
            ->get();

        $teamStats = [];

        $allMatches = LeagueMatches::with(['homeTeam', 'awayTeam'])
            ->where('season_id', $seasonId)
            ->get();

        foreach ($allMatches as $match) {
            if (!isset($teamStats[$match->homeTeam->name])) {
                $teamStats[$match->homeTeam->name] = [
                    'team' => $match->homeTeam->name,
                    'played' => 0,
                    'won' => 0,
                    'drawn' => 0,
                    'lost' => 0,
                    'points' => 0
                ];
            }
            if (!isset($teamStats[$match->awayTeam->name])) {
                $teamStats[$match->awayTeam->name] = [
                    'team' => $match->awayTeam->name,
                    'played' => 0,
                    'won' => 0,
                    'drawn' => 0,
                    'lost' => 0,
                    'points' => 0
                ];
            }
        }

        foreach ($matches as $match) {
            $homeTeam = $match->homeTeam->name;
            $awayTeam = $match->awayTeam->name;
            $homeScore = $match->home_score ?? 0;
            $awayScore = $match->away_score ?? 0;

            $teamStats[$homeTeam]['played']++;
            $teamStats[$awayTeam]['played']++;

            if ($homeScore > $awayScore) {
                $teamStats[$homeTeam]['won']++;
                $teamStats[$homeTeam]['points'] += 3;
                $teamStats[$awayTeam]['lost']++;
            } elseif ($homeScore < $awayScore) {
                $teamStats[$awayTeam]['won']++;
                $teamStats[$awayTeam]['points'] += 3;
                $teamStats[$homeTeam]['lost']++;
            } else {
                $teamStats[$homeTeam]['drawn']++;
                $teamStats[$awayTeam]['drawn']++;
                $teamStats[$homeTeam]['points'] += 1;
                $teamStats[$awayTeam]['points'] += 1;
            }
        }

        $standings = array_values($teamStats);
        usort($standings, function ($a, $b) {
            return $b['points'] - $a['points'];
        });

        return $standings;
    }

    public function calculateChampionshipPredictions(array $standings, int $currentWeek): array
    {
        // Only calculate predictions if current week > 3
        if ($currentWeek < 4) {
            return [];
        }

        $totalPoints = array_sum(array_column($standings, 'points'));
        $predictions = [];

        if ($totalPoints === 0) {
            $equalChance = round(100 / count($standings));
            foreach ($standings as $team) {
                $predictions[$team['team']] = $equalChance;
            }
        } else {
            foreach ($standings as $index => $team) {
                $baseChance = $totalPoints > 0 ? ($team['points'] / $totalPoints) * 100 : 25;
                $positionBonus = (count($standings) - $index) * 5;
                $predictions[$team['team']] = min(round($baseChance + $positionBonus), 100);
            }
        }

        return $predictions;
    }

    public function getCurrentWeek(string $seasonId): int
    {
        $nextUnplayedWeek = LeagueMatches::where('season_id', $seasonId)
            ->where('is_played', false)
            ->min('week');

        return $nextUnplayedWeek ?? 1;
    }

    public function isSeasonComplete(string $seasonId): bool
    {
        $maxWeek = LeagueMatches::where('season_id', $seasonId)->max('week') ?? 0;

        if ($maxWeek < 6) {
            return false;
        }

        $lastWeekMatches = LeagueMatches::where('season_id', $seasonId)
            ->where('week', $maxWeek)
            ->get();

        return $lastWeekMatches->every(fn($match) => $match->is_played);
    }
}
