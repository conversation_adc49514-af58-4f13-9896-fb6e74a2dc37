<?php

namespace App\Providers;

use App\Services\FixtureGenerator;
use App\Services\SimulationService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(FixtureGenerator::class, function ($app) {
            return new FixtureGenerator();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
