import { Match } from '@/types/simulation';

export function groupFixturesByWeek(fixtures: Match[]) {
    const groupedByWeek: Record<number, Match[]> = {};

    for (const match of fixtures) {
        const weekNumber = match.week;

        if (!groupedByWeek[weekNumber]) {
            groupedByWeek[weekNumber] = [];
        }

        groupedByWeek[weekNumber].push(match);
    }
    return groupedByWeek;
}
